import hre from 'hardhat';
import { parseUnits, toHex, formatUnits } from 'viem';
import { expect } from 'chai';
import { loadFixture } from '@nomicfoundation/hardhat-toolbox-viem/network-helpers';
import { getEventsFromTx, walletAddress } from './utilities/helpers.ts';
import { time } from "@nomicfoundation/hardhat-network-helpers";

// Constants for testing - matching real world scenario
const antiSpamFee = parseUnits('1', 18);

const deployUpdraft = async () => {
  const upd = await hre.viem.deployContract('UPDToken');
  const feeToken = upd.address;
  const percentFee = 10000; // 1%
  const accrualRate = 100000; // 10% - higher for easier testing
  const cycleLength = 12 * 3600; // 12 hours in seconds (real world)
  const humanity = '******************************************';
  const args = [feeToken, antiSpamFee, percentFee, cycleLength, accrualRate, humanity];
  const updraft = await hre.viem.deployContract('Updraft', args);
  return { updraft, upd, humanity };
};

const deployUpdraftAndApproveToSpendUPD = async () => {
  const { updraft, upd, humanity } = await loadFixture(deployUpdraft);
  // Approve a very large amount to avoid allowance issues
  await upd.write.approve([updraft.address, parseUnits('10000000', 18)]);
  return { updraft, upd, humanity };
};

describe('Real World Scenario Analysis', () => {
  it('should reproduce the exact real-world scenario with correct parameters', async () => {
    const { updraft, upd, humanity } = await loadFixture(deployUpdraftAndApproveToSpendUPD);
    const publicClient = await hre.viem.getPublicClient();

    // Create idea with 5% contributor fee (real world parameter)
    const contributorFee = 50000; // 5% (not 10%)
    const initialContribution = parseUnits('500', 18); // 500 UPD
    const hash = await updraft.write.createIdea([contributorFee, initialContribution, toHex({})]);
    const transaction = await publicClient.getTransactionReceipt({hash});
    const events = await getEventsFromTx('Updraft', transaction);
    const { idea } = events.find(event => event.eventName === 'IdeaCreated').args;
    const contract = await hre.viem.getContractAt('Idea', idea);

    // Get wallet clients to simulate the exact addresses from the transaction data
    const [creatorWallet, largeContributorWallet, smallContributor1Wallet, smallContributor2Wallet, airdropperWallet] = await hre.viem.getWalletClients();
    
    // Transfer tokens to test wallets
    const transferAmount = parseUnits('1000000', 18);
    await upd.write.transfer([largeContributorWallet.account.address, transferAmount]);
    await upd.write.transfer([smallContributor1Wallet.account.address, transferAmount]);
    await upd.write.transfer([smallContributor2Wallet.account.address, transferAmount]);
    await upd.write.transfer([airdropperWallet.account.address, transferAmount]);

    // Approve contracts to spend tokens
    await upd.write.approve([contract.address, transferAmount], { account: largeContributorWallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: smallContributor1Wallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: smallContributor2Wallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: airdropperWallet.account });
    await upd.write.approve([contract.address, parseUnits('100000', 18)]);

    console.log('\n=== Reproducing Real World Transaction Sequence ===');
    console.log('Contributor fee: 5%');
    console.log('Cycle length: 12 hours');
    
    // All these contributions happened in the FIRST CYCLE according to timestamps
    // Transaction 1: Large contributor contributes 55,555 UPD (June 12, 10:35:49)
    const largeContribution = parseUnits('55555', 18);
    await contract.write.contribute([largeContribution], { account: largeContributorWallet.account });
    console.log(`Large contributor: ${formatUnits(largeContribution, 18)} UPD`);

    // Transaction 2: Small contributor 1 contributes 1,000 UPD (June 12, 16:03:21 - same day)
    const smallContribution1 = parseUnits('1000', 18);
    await contract.write.contribute([smallContribution1], { account: smallContributor1Wallet.account });
    console.log(`Small contributor 1: ${formatUnits(smallContribution1, 18)} UPD`);

    // Transaction 3: Small contributor 2 contributes 20,000 UPD (June 12, 20:19:15 - same day)
    const smallContribution2 = parseUnits('20000', 18);
    await contract.write.contribute([smallContribution2], { account: smallContributor2Wallet.account });
    console.log(`Small contributor 2: ${formatUnits(smallContribution2, 18)} UPD`);

    // Transaction 4: Small contributor 1 contributes another 1,000 UPD (June 29, 09:43:17)
    // This is 17 days later, so let's calculate how many 12-hour cycles that is
    const daysLater = 17;
    const cyclesLater = daysLater * 2; // 2 cycles per day (12 hour cycles)
    const secondsToAdvance = cyclesLater * 12 * 3600;
    
    await time.increase(secondsToAdvance);
    await contract.write.contribute([smallContribution1], { account: smallContributor1Wallet.account });
    console.log(`Small contributor 1 (${daysLater} days later): ${formatUnits(smallContribution1, 18)} UPD`);

    // Get cycle length
    const cycleLength = await contract.read.cycleLength();
    
    // Now simulate time passing from June 29 to July 29 (30 more days)
    const moreDaysLater = 30;
    const moreCyclesLater = moreDaysLater * 2;
    const moreSecondsToAdvance = moreCyclesLater * 12 * 3600;
    
    await time.increase(moreSecondsToAdvance);
    
    // Make a small contribution to update cycles
    await contract.write.contribute([antiSpamFee * 2n]);

    // Transaction 5: Airdrop of 600,000 UPD (July 29)
    console.log('\n=== Airdrop Transaction ===');
    const airdropAmount = parseUnits('600000', 18);
    await contract.write.airdrop([airdropAmount], { account: airdropperWallet.account });
    console.log(`Airdropped: ${formatUnits(airdropAmount, 18)} UPD`);

    // Advance time to distribute fees
    await time.increase(Number(cycleLength) + 1);
    await contract.write.contribute([antiSpamFee * 2n]);

    // Check positions before withdrawals
    console.log('\n=== Positions Before Withdrawals ===');
    const largeContributorPosition = await contract.read.checkPosition([largeContributorWallet.account.address, 0]);
    const smallContributor1Position1 = await contract.read.checkPosition([smallContributor1Wallet.account.address, 0]);
    const smallContributor1Position2 = await contract.read.checkPosition([smallContributor1Wallet.account.address, 1]);
    
    console.log(`Large contributor position: ${formatUnits(largeContributorPosition[0], 18)} UPD, shares: ${formatUnits(largeContributorPosition[1], 18)}`);
    console.log(`Small contributor 1 position 1: ${formatUnits(smallContributor1Position1[0], 18)} UPD, shares: ${formatUnits(smallContributor1Position1[1], 18)}`);
    console.log(`Small contributor 1 position 2: ${formatUnits(smallContributor1Position2[0], 18)} UPD, shares: ${formatUnits(smallContributor1Position2[1], 18)}`);

    // Get contract state before withdrawals
    const totalTokensBefore = await contract.read.tokens();
    const contributorFeesBefore = await contract.read.contributorFees();
    console.log(`\nTotal tokens in contract: ${formatUnits(totalTokensBefore, 18)} UPD`);
    console.log(`Contributor fees in contract: ${formatUnits(contributorFeesBefore, 18)} UPD`);

    // Transaction 6: Small contributor 1 withdraws first position (July 28 - should get 495.32 UPD)
    console.log('\n=== First Withdrawal ===');
    const smallContributor1BalanceBefore = await upd.read.balanceOf([smallContributor1Wallet.account.address]);
    await contract.write.withdraw([0], { account: smallContributor1Wallet.account });
    const smallContributor1BalanceAfter = await upd.read.balanceOf([smallContributor1Wallet.account.address]);
    const firstWithdrawal = smallContributor1BalanceAfter - smallContributor1BalanceBefore;
    console.log(`Small contributor 1 first withdrawal: ${formatUnits(firstWithdrawal, 18)} UPD (real world: 495.32)`);

    // Transaction 7: Small contributor 1 withdraws second position (July 29 - should get 940.5 UPD)
    console.log('\n=== Second Withdrawal ===');
    await contract.write.withdraw([1], { account: smallContributor1Wallet.account });
    const smallContributor1BalanceFinal = await upd.read.balanceOf([smallContributor1Wallet.account.address]);
    const secondWithdrawal = smallContributor1BalanceFinal - smallContributor1BalanceAfter;
    console.log(`Small contributor 1 second withdrawal: ${formatUnits(secondWithdrawal, 18)} UPD (real world: 940.5)`);

    const totalSmallContributor1Withdrawn = smallContributor1BalanceFinal - smallContributor1BalanceBefore;
    console.log(`Small contributor 1 total withdrawn: ${formatUnits(totalSmallContributor1Withdrawn, 18)} UPD (real world: 1435.82)`);
    console.log(`Small contributor 1 total deposited: ${formatUnits(smallContribution1 * 2n, 18)} UPD`);
    console.log(`Small contributor 1 gain: ${formatUnits(totalSmallContributor1Withdrawn - (smallContribution1 * 2n), 18)} UPD`);

    // Transaction 8: Large contributor withdraws (August 5 - should get only ~55,035 UPD according to your data)
    console.log('\n=== Large Contributor Withdrawal ===');
    const largeContributorBalanceBefore = await upd.read.balanceOf([largeContributorWallet.account.address]);
    await contract.write.withdraw([0], { account: largeContributorWallet.account });
    const largeContributorBalanceAfter = await upd.read.balanceOf([largeContributorWallet.account.address]);
    const largeContributorWithdrawn = largeContributorBalanceAfter - largeContributorBalanceBefore;
    
    console.log(`Large contributor withdrew: ${formatUnits(largeContributorWithdrawn, 18)} UPD (real world: ~55,035)`);
    console.log(`Large contributor deposited: ${formatUnits(largeContribution, 18)} UPD`);
    console.log(`Large contributor gain: ${formatUnits(largeContributorWithdrawn - largeContribution, 18)} UPD`);

    // Check final contract state
    const finalTokens = await contract.read.tokens();
    const finalContributorFees = await contract.read.contributorFees();
    console.log(`\n=== Final Contract State ===`);
    console.log(`Tokens remaining: ${formatUnits(finalTokens, 18)} UPD (real world: ~600k+)`);
    console.log(`Contributor fees remaining: ${formatUnits(finalContributorFees, 18)} UPD`);

    // Analyze the results
    console.log(`\n=== Analysis ===`);
    
    // Check if our simulation matches the real world behavior
    const realWorldLargeWithdrawal = parseUnits('55035', 18);
    const realWorldSmallWithdrawal = parseUnits('1435.82', 18);
    
    const largeWithdrawalDiff = largeContributorWithdrawn > realWorldLargeWithdrawal ? 
      largeContributorWithdrawn - realWorldLargeWithdrawal : 
      realWorldLargeWithdrawal - largeContributorWithdrawn;
      
    const smallWithdrawalDiff = totalSmallContributor1Withdrawn > realWorldSmallWithdrawal ?
      totalSmallContributor1Withdrawn - realWorldSmallWithdrawal :
      realWorldSmallWithdrawal - totalSmallContributor1Withdrawn;

    console.log(`Large contributor difference from real world: ${formatUnits(largeWithdrawalDiff, 18)} UPD`);
    console.log(`Small contributor difference from real world: ${formatUnits(smallWithdrawalDiff, 18)} UPD`);

    if (finalTokens > parseUnits('100000', 18)) {
      console.log(`🐛 ISSUE CONFIRMED: ${formatUnits(finalTokens, 18)} UPD stuck in contract!`);
    }

    // The large contributor should have gotten much more from the 600k airdrop
    const expectedMinimumFromAirdrop = parseUnits('300000', 18); // Should get majority of 600k
    const actualGain = largeContributorWithdrawn - largeContribution;
    
    if (actualGain < expectedMinimumFromAirdrop) {
      console.log(`🐛 AIRDROP DISTRIBUTION ISSUE: Large contributor only gained ${formatUnits(actualGain, 18)} UPD from 600k airdrop`);
    }
  });
});
