import hre from 'hardhat';
import { parseUnits, toHex, formatUnits } from 'viem';
import { expect } from 'chai';
import { loadFixture } from '@nomicfoundation/hardhat-toolbox-viem/network-helpers';
import { getEventsFromTx, walletAddress } from './utilities/helpers.ts';
import { time } from "@nomicfoundation/hardhat-network-helpers";

// Constants for testing
const antiSpamFee = parseUnits('1', 18);

const deployUpdraft = async () => {
  const upd = await hre.viem.deployContract('UPDToken');
  const feeToken = upd.address;
  const percentFee = 10000; // 1%
  const accrualRate = 100000; // 10% - higher for easier testing
  const cycleLength = 3600; // 1 hour in seconds
  const humanity = '******************************************';
  const args = [feeToken, antiSpamFee, percentFee, cycleLength, accrualRate, humanity];
  const updraft = await hre.viem.deployContract('Updraft', args);
  return { updraft, upd, humanity };
};

const deployUpdraftAndApproveToSpendUPD = async () => {
  const { updraft, upd, humanity } = await loadFixture(deployUpdraft);
  // Approve a very large amount to avoid allowance issues
  await upd.write.approve([updraft.address, parseUnits('10000000', 18)]);
  return { updraft, upd, humanity };
};

describe('Airdrop and Withdrawal Bug Reproduction', () => {
  it('should reproduce the bug where large contributor gets barely more than deposit after airdrop', async () => {
    const { updraft, upd, humanity } = await loadFixture(deployUpdraftAndApproveToSpendUPD);
    const publicClient = await hre.viem.getPublicClient();

    // Create a new idea with 10% contributor fee (similar to the real scenario)
    const contributorFee = 100000; // 10%
    const initialContribution = parseUnits('500', 18); // 500 UPD
    const hash = await updraft.write.createIdea([contributorFee, initialContribution, toHex({})]);
    const transaction = await publicClient.getTransactionReceipt({hash});
    const events = await getEventsFromTx('Updraft', transaction);
    const { idea } = events.find(event => event.eventName === 'IdeaCreated').args;
    const contract = await hre.viem.getContractAt('Idea', idea);

    // Get wallet clients - we'll simulate the scenario from the transaction data
    const [creatorWallet, largeContributorWallet, smallContributorWallet, airdropperWallet] = await hre.viem.getWalletClients();
    
    // Transfer tokens to test wallets
    const transferAmount = parseUnits('1000000', 18); // 1M UPD each
    await upd.write.transfer([largeContributorWallet.account.address, transferAmount]);
    await upd.write.transfer([smallContributorWallet.account.address, transferAmount]);
    await upd.write.transfer([airdropperWallet.account.address, transferAmount]);

    // Approve contracts to spend tokens
    await upd.write.approve([contract.address, transferAmount], { account: largeContributorWallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: smallContributorWallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: airdropperWallet.account });

    // Also approve the main wallet to spend tokens for small contributions
    await upd.write.approve([contract.address, parseUnits('100000', 18)]);

    console.log('\n=== Initial Contributions (First Cycle) ===');
    
    // Simulate the initial contributions from the transaction data
    // Large contributor: 55,555 UPD
    const largeContribution = parseUnits('55555', 18);
    await contract.write.contribute([largeContribution], { account: largeContributorWallet.account });
    console.log(`Large contributor deposited: ${formatUnits(largeContribution, 18)} UPD`);

    // Small contributors: 1,000 UPD and 20,000 UPD
    const smallContribution1 = parseUnits('1000', 18);
    const smallContribution2 = parseUnits('20000', 18);
    await contract.write.contribute([smallContribution1], { account: smallContributorWallet.account });
    await contract.write.contribute([smallContribution2], { account: smallContributorWallet.account });
    console.log(`Small contributor deposited: ${formatUnits(smallContribution1 + smallContribution2, 18)} UPD`);

    // Get cycle length
    const cycleLength = await contract.read.cycleLength();
    
    // Advance to second cycle (this is when contributor fees start being charged)
    console.log('\n=== Advancing to Second Cycle ===');
    await time.increase(Number(cycleLength) + 1);
    
    // Make a small contribution to trigger cycle update
    await contract.write.contribute([antiSpamFee * 2n]);
    
    // Check positions after first cycle
    const largeContributorPosition = await contract.read.checkPosition([largeContributorWallet.account.address, 0]);
    const smallContributorPosition1 = await contract.read.checkPosition([smallContributorWallet.account.address, 0]);
    const smallContributorPosition2 = await contract.read.checkPosition([smallContributorWallet.account.address, 1]);
    
    console.log(`Large contributor position: ${formatUnits(largeContributorPosition[0], 18)} UPD, shares: ${formatUnits(largeContributorPosition[1], 18)}`);
    console.log(`Small contributor position 1: ${formatUnits(smallContributorPosition1[0], 18)} UPD, shares: ${formatUnits(smallContributorPosition1[1], 18)}`);
    console.log(`Small contributor position 2: ${formatUnits(smallContributorPosition2[0], 18)} UPD, shares: ${formatUnits(smallContributorPosition2[1], 18)}`);

    // Advance more cycles to simulate time passing (like in the real scenario)
    console.log('\n=== Advancing Multiple Cycles ===');
    for (let i = 0; i < 10; i++) {
      await time.increase(Number(cycleLength) + 1);
      await contract.write.contribute([antiSpamFee * 2n]);
    }

    // Now perform the large airdrop (600,000 UPD like in the real scenario)
    console.log('\n=== Large Airdrop ===');
    const airdropAmount = parseUnits('600000', 18);
    await contract.write.airdrop([airdropAmount], { account: airdropperWallet.account });
    console.log(`Airdropped: ${formatUnits(airdropAmount, 18)} UPD`);

    // Advance time to distribute the airdrop fees
    await time.increase(Number(cycleLength) + 1);
    await contract.write.contribute([antiSpamFee * 2n]);

    // Check positions after airdrop
    console.log('\n=== Positions After Airdrop ===');
    const largeContributorPositionAfterAirdrop = await contract.read.checkPosition([largeContributorWallet.account.address, 0]);
    const smallContributorPosition1AfterAirdrop = await contract.read.checkPosition([smallContributorWallet.account.address, 0]);
    const smallContributorPosition2AfterAirdrop = await contract.read.checkPosition([smallContributorWallet.account.address, 1]);
    
    console.log(`Large contributor position: ${formatUnits(largeContributorPositionAfterAirdrop[0], 18)} UPD, shares: ${formatUnits(largeContributorPositionAfterAirdrop[1], 18)}`);
    console.log(`Small contributor position 1: ${formatUnits(smallContributorPosition1AfterAirdrop[0], 18)} UPD, shares: ${formatUnits(smallContributorPosition1AfterAirdrop[1], 18)}`);
    console.log(`Small contributor position 2: ${formatUnits(smallContributorPosition2AfterAirdrop[0], 18)} UPD, shares: ${formatUnits(smallContributorPosition2AfterAirdrop[1], 18)}`);

    // Get contract state before withdrawals
    const totalTokensBefore = await contract.read.tokens();
    const contributorFeesBefore = await contract.read.contributorFees();
    console.log(`\nTotal tokens in contract: ${formatUnits(totalTokensBefore, 18)} UPD`);
    console.log(`Contributor fees in contract: ${formatUnits(contributorFeesBefore, 18)} UPD`);

    // Track balances before withdrawals
    const largeContributorBalanceBefore = await upd.read.balanceOf([largeContributorWallet.account.address]);
    const smallContributorBalanceBefore = await upd.read.balanceOf([smallContributorWallet.account.address]);

    // Large contributor withdraws (this should give them a fair share of the airdrop)
    console.log('\n=== Large Contributor Withdrawal ===');
    await contract.write.withdraw([0], { account: largeContributorWallet.account });
    
    const largeContributorBalanceAfter = await upd.read.balanceOf([largeContributorWallet.account.address]);
    const largeContributorWithdrawn = largeContributorBalanceAfter - largeContributorBalanceBefore;
    console.log(`Large contributor withdrew: ${formatUnits(largeContributorWithdrawn, 18)} UPD`);
    console.log(`Original contribution: ${formatUnits(largeContribution, 18)} UPD`);
    console.log(`Gain from fees: ${formatUnits(largeContributorWithdrawn - largeContribution, 18)} UPD`);

    // Check remaining tokens in contract
    const totalTokensAfterLargeWithdrawal = await contract.read.tokens();
    const contributorFeesAfterLargeWithdrawal = await contract.read.contributorFees();
    console.log(`\nTokens remaining after large withdrawal: ${formatUnits(totalTokensAfterLargeWithdrawal, 18)} UPD`);
    console.log(`Contributor fees remaining: ${formatUnits(contributorFeesAfterLargeWithdrawal, 18)} UPD`);

    // Test the bug: Large contributor should have gotten a significant portion of the 600k airdrop
    // but based on your data, they only got ~55k total (barely more than their 55,555 deposit)
    // This suggests the fee distribution is broken
    
    // The large contributor had the biggest position and should have gotten the majority of the airdrop
    const expectedMinimumGain = parseUnits('100000', 18); // Should get at least 100k from the 600k airdrop
    const actualGain = largeContributorWithdrawn - largeContribution;
    
    console.log(`\n=== Bug Analysis ===`);
    console.log(`Expected minimum gain: ${formatUnits(expectedMinimumGain, 18)} UPD`);
    console.log(`Actual gain: ${formatUnits(actualGain, 18)} UPD`);
    
    if (actualGain < expectedMinimumGain) {
      console.log(`🐛 BUG REPRODUCED: Large contributor got much less than expected!`);
      console.log(`This indicates a problem with fee distribution in the Idea contract.`);
    }

    // Now test the double withdrawal issue
    console.log('\n=== Testing Double Withdrawal Issue ===');
    
    // Small contributor withdraws first position
    await contract.write.withdraw([0], { account: smallContributorWallet.account });
    const smallContributorBalanceAfterFirst = await upd.read.balanceOf([smallContributorWallet.account.address]);
    const firstWithdrawal = smallContributorBalanceAfterFirst - smallContributorBalanceBefore;
    console.log(`Small contributor first withdrawal: ${formatUnits(firstWithdrawal, 18)} UPD`);

    // Small contributor withdraws second position
    await contract.write.withdraw([1], { account: smallContributorWallet.account });
    const smallContributorBalanceAfterSecond = await upd.read.balanceOf([smallContributorWallet.account.address]);
    const secondWithdrawal = smallContributorBalanceAfterSecond - smallContributorBalanceAfterFirst;
    console.log(`Small contributor second withdrawal: ${formatUnits(secondWithdrawal, 18)} UPD`);
    
    const totalSmallContributorWithdrawn = smallContributorBalanceAfterSecond - smallContributorBalanceBefore;
    const totalSmallContributorDeposited = smallContribution1 + smallContribution2;
    console.log(`Small contributor total withdrawn: ${formatUnits(totalSmallContributorWithdrawn, 18)} UPD`);
    console.log(`Small contributor total deposited: ${formatUnits(totalSmallContributorDeposited, 18)} UPD`);

    // Final contract state
    const finalTokens = await contract.read.tokens();
    const finalContributorFees = await contract.read.contributorFees();
    console.log(`\n=== Final Contract State ===`);
    console.log(`Tokens remaining: ${formatUnits(finalTokens, 18)} UPD`);
    console.log(`Contributor fees remaining: ${formatUnits(finalContributorFees, 18)} UPD`);

    // The bug is that there are still significant tokens left in the contract
    // that should have been distributed to contributors
    if (finalTokens > parseUnits('1000', 18)) { // More than 1000 UPD left
      console.log(`🐛 BUG CONFIRMED: ${formatUnits(finalTokens, 18)} UPD stuck in contract!`);
    }
  });

  it('should reproduce the exact scenario from transaction data', async () => {
    const { updraft, upd, humanity } = await loadFixture(deployUpdraftAndApproveToSpendUPD);
    const publicClient = await hre.viem.getPublicClient();

    // Create a new idea with 10% contributor fee
    const contributorFee = 100000; // 10%
    const initialContribution = parseUnits('500', 18); // 500 UPD
    const hash = await updraft.write.createIdea([contributorFee, initialContribution, toHex({})]);
    const transaction = await publicClient.getTransactionReceipt({hash});
    const events = await getEventsFromTx('Updraft', transaction);
    const { idea } = events.find(event => event.eventName === 'IdeaCreated').args;
    const contract = await hre.viem.getContractAt('Idea', idea);

    // Get wallet clients to simulate the exact addresses from the transaction data
    const [creatorWallet, largeContributorWallet, smallContributor1Wallet, smallContributor2Wallet, airdropperWallet] = await hre.viem.getWalletClients();

    // Transfer tokens to test wallets
    const transferAmount = parseUnits('1000000', 18);
    await upd.write.transfer([largeContributorWallet.account.address, transferAmount]);
    await upd.write.transfer([smallContributor1Wallet.account.address, transferAmount]);
    await upd.write.transfer([smallContributor2Wallet.account.address, transferAmount]);
    await upd.write.transfer([airdropperWallet.account.address, transferAmount]);

    // Approve contracts to spend tokens
    await upd.write.approve([contract.address, transferAmount], { account: largeContributorWallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: smallContributor1Wallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: smallContributor2Wallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: airdropperWallet.account });
    await upd.write.approve([contract.address, parseUnits('100000', 18)]);

    console.log('\n=== Reproducing Exact Transaction Sequence ===');

    // Transaction 1: Large contributor contributes 55,555 UPD
    const largeContribution = parseUnits('55555', 18);
    await contract.write.contribute([largeContribution], { account: largeContributorWallet.account });
    console.log(`Large contributor (0xdC00...): ${formatUnits(largeContribution, 18)} UPD`);

    // Transaction 2: Small contributor 1 contributes 1,000 UPD
    const smallContribution1 = parseUnits('1000', 18);
    await contract.write.contribute([smallContribution1], { account: smallContributor1Wallet.account });
    console.log(`Small contributor 1 (0x5299...): ${formatUnits(smallContribution1, 18)} UPD`);

    // Transaction 3: Small contributor 2 contributes 20,000 UPD
    const smallContribution2 = parseUnits('20000', 18);
    await contract.write.contribute([smallContribution2], { account: smallContributor2Wallet.account });
    console.log(`Small contributor 2 (0x85BF...): ${formatUnits(smallContribution2, 18)} UPD`);

    // Transaction 4: Small contributor 1 contributes another 1,000 UPD
    await contract.write.contribute([smallContribution1], { account: smallContributor1Wallet.account });
    console.log(`Small contributor 1 (0x9F08...): ${formatUnits(smallContribution1, 18)} UPD`);

    // Get cycle length
    const cycleLength = await contract.read.cycleLength();

    // Simulate time passing (from June 12 to July 29 - about 47 days)
    const daysToPass = 47;
    const secondsToPass = daysToPass * 24 * 3600;
    await time.increase(secondsToPass);

    // Make a small contribution to update cycles
    await contract.write.contribute([antiSpamFee * 2n]);

    // Transaction 5: Airdrop of 600,000 UPD
    console.log('\n=== Airdrop Transaction ===');
    const airdropAmount = parseUnits('600000', 18);
    await contract.write.airdrop([airdropAmount], { account: airdropperWallet.account });
    console.log(`Airdropped: ${formatUnits(airdropAmount, 18)} UPD`);

    // Advance time to distribute fees
    await time.increase(Number(cycleLength) + 1);
    await contract.write.contribute([antiSpamFee * 2n]);

    // Check positions before withdrawals
    console.log('\n=== Positions Before Withdrawals ===');
    const largeContributorPosition = await contract.read.checkPosition([largeContributorWallet.account.address, 0]);
    console.log(`Large contributor position: ${formatUnits(largeContributorPosition[0], 18)} UPD`);

    // Transaction 6: Small contributor 1 withdraws first position (should get 495.32 UPD)
    console.log('\n=== First Withdrawal ===');
    const smallContributor1BalanceBefore = await upd.read.balanceOf([smallContributor1Wallet.account.address]);
    await contract.write.withdraw([0], { account: smallContributor1Wallet.account });
    const smallContributor1BalanceAfter = await upd.read.balanceOf([smallContributor1Wallet.account.address]);
    const firstWithdrawal = smallContributor1BalanceAfter - smallContributor1BalanceBefore;
    console.log(`Small contributor 1 first withdrawal: ${formatUnits(firstWithdrawal, 18)} UPD (expected ~495.32)`);

    // Transaction 7: Small contributor 1 withdraws second position (should get 940.5 UPD)
    console.log('\n=== Second Withdrawal ===');
    await contract.write.withdraw([1], { account: smallContributor1Wallet.account });
    const smallContributor1BalanceFinal = await upd.read.balanceOf([smallContributor1Wallet.account.address]);
    const secondWithdrawal = smallContributor1BalanceFinal - smallContributor1BalanceAfter;
    console.log(`Small contributor 1 second withdrawal: ${formatUnits(secondWithdrawal, 18)} UPD (expected ~940.5)`);

    const totalSmallContributor1Withdrawn = smallContributor1BalanceFinal - smallContributor1BalanceBefore;
    console.log(`Small contributor 1 total withdrawn: ${formatUnits(totalSmallContributor1Withdrawn, 18)} UPD`);
    console.log(`Small contributor 1 total deposited: ${formatUnits(smallContribution1 * 2n, 18)} UPD`);
    console.log(`Small contributor 1 gain: ${formatUnits(totalSmallContributor1Withdrawn - (smallContribution1 * 2n), 18)} UPD`);

    // Transaction 8: Large contributor withdraws (should get only ~55,035 UPD according to your data)
    console.log('\n=== Large Contributor Withdrawal ===');
    const largeContributorBalanceBefore = await upd.read.balanceOf([largeContributorWallet.account.address]);
    await contract.write.withdraw([0], { account: largeContributorWallet.account });
    const largeContributorBalanceAfter = await upd.read.balanceOf([largeContributorWallet.account.address]);
    const largeContributorWithdrawn = largeContributorBalanceAfter - largeContributorBalanceBefore;

    console.log(`Large contributor withdrew: ${formatUnits(largeContributorWithdrawn, 18)} UPD (expected ~55,035 based on your data)`);
    console.log(`Large contributor deposited: ${formatUnits(largeContribution, 18)} UPD`);
    console.log(`Large contributor gain: ${formatUnits(largeContributorWithdrawn - largeContribution, 18)} UPD`);

    // Check final contract state
    const finalTokens = await contract.read.tokens();
    const finalContributorFees = await contract.read.contributorFees();
    console.log(`\n=== Final Contract State ===`);
    console.log(`Tokens remaining: ${formatUnits(finalTokens, 18)} UPD (your data shows ~600k+ remaining)`);
    console.log(`Contributor fees remaining: ${formatUnits(finalContributorFees, 18)} UPD`);

    // Analyze the discrepancy
    console.log(`\n=== Analysis ===`);
    if (largeContributorWithdrawn < parseUnits('60000', 18)) {
      console.log(`🐛 BUG REPRODUCED: Large contributor got much less than expected!`);
    } else {
      console.log(`❓ DISCREPANCY: Our test shows different results than the real transaction data.`);
      console.log(`This suggests either:`);
      console.log(`1. The deployed contract has different logic`);
      console.log(`2. There were additional transactions not shown in the data`);
      console.log(`3. The contract state was different due to other factors`);
    }

    if (finalTokens > parseUnits('100000', 18)) {
      console.log(`🐛 CONFIRMED: Large amount of tokens stuck in contract!`);
    }
  });
});
