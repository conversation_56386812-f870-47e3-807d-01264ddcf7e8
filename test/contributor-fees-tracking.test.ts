import hre from 'hardhat';
import { parseUnits, toHex, formatUnits } from 'viem';
import { expect } from 'chai';
import { loadFixture } from '@nomicfoundation/hardhat-toolbox-viem/network-helpers';
import { getEventsFromTx, walletAddress } from './utilities/helpers.ts';
import { time } from "@nomicfoundation/hardhat-network-helpers";

// Constants for testing
const antiSpamFee = parseUnits('1', 18);

const deployUpdraft = async () => {
  const upd = await hre.viem.deployContract('UPDToken');
  const feeToken = upd.address;
  const percentFee = 10000; // 1%
  const accrualRate = 100000; // 10% - higher for easier testing
  const cycleLength = 3600; // 1 hour in seconds
  const humanity = '******************************************';
  const args = [feeToken, antiSpamFee, percentFee, cycleLength, accrualRate, humanity];
  const updraft = await hre.viem.deployContract('Updraft', args);
  return { updraft, upd, humanity };
};

const deployUpdraftAndApproveToSpendUPD = async () => {
  const { updraft, upd, humanity } = await loadFixture(deployUpdraft);
  // Approve a very large amount to avoid allowance issues
  await upd.write.approve([updraft.address, parseUnits('10000000', 18)]);
  return { updraft, upd, humanity };
};

describe('Contributor Fees Tracking Analysis', () => {
  it('should analyze how contributorFees variable affects withdrawals', async () => {
    const { updraft, upd, humanity } = await loadFixture(deployUpdraftAndApproveToSpendUPD);
    const publicClient = await hre.viem.getPublicClient();

    // Create idea with 5% contributor fee
    const contributorFee = 50000; // 5%
    const initialContribution = parseUnits('1000', 18);
    const hash = await updraft.write.createIdea([contributorFee, initialContribution, toHex({})]);
    const transaction = await publicClient.getTransactionReceipt({hash});
    const events = await getEventsFromTx('Updraft', transaction);
    const { idea } = events.find(event => event.eventName === 'IdeaCreated').args;
    const contract = await hre.viem.getContractAt('Idea', idea);

    // Get wallet clients
    const [firstWallet, secondWallet, airdropperWallet] = await hre.viem.getWalletClients();
    
    // Transfer tokens to test wallets
    const transferAmount = parseUnits('1000000', 18);
    await upd.write.transfer([secondWallet.account.address, transferAmount]);
    await upd.write.transfer([airdropperWallet.account.address, transferAmount]);
    await upd.write.approve([contract.address, transferAmount], { account: secondWallet.account });
    await upd.write.approve([contract.address, transferAmount], { account: airdropperWallet.account });
    await upd.write.approve([contract.address, parseUnits('100000', 18)]);

    console.log('\n=== Analyzing Contributor Fees Tracking ===');
    
    // Second wallet contributes in first cycle
    const secondContribution = parseUnits('2000', 18);
    await contract.write.contribute([secondContribution], { account: secondWallet.account });
    console.log(`Second wallet contributed: ${formatUnits(secondContribution, 18)} UPD in first cycle`);

    // Check initial state
    let totalTokens = await contract.read.tokens();
    let contributorFees = await contract.read.contributorFees();
    console.log(`After first cycle contributions:`);
    console.log(`  Total tokens: ${formatUnits(totalTokens, 18)} UPD`);
    console.log(`  Contributor fees: ${formatUnits(contributorFees, 18)} UPD`);

    // Get cycle length and advance to second cycle
    const cycleLength = await contract.read.cycleLength();
    await time.increase(Number(cycleLength) + 1);
    
    // Make contributions in second cycle (this will charge contributor fees)
    const thirdContribution = parseUnits('1000', 18);
    await contract.write.contribute([thirdContribution]);
    console.log(`\nFirst wallet contributed: ${formatUnits(thirdContribution, 18)} UPD in second cycle`);

    // Check state after second cycle contribution
    totalTokens = await contract.read.tokens();
    contributorFees = await contract.read.contributorFees();
    console.log(`After second cycle contribution:`);
    console.log(`  Total tokens: ${formatUnits(totalTokens, 18)} UPD`);
    console.log(`  Contributor fees: ${formatUnits(contributorFees, 18)} UPD`);

    // Calculate expected contributor fee
    const expectedContributorFee = thirdContribution * BigInt(contributorFee) / BigInt(1000000);
    console.log(`  Expected contributor fee from this contribution: ${formatUnits(expectedContributorFee, 18)} UPD`);

    // Advance to third cycle and make more contributions
    await time.increase(Number(cycleLength) + 1);
    const fourthContribution = parseUnits('500', 18);
    await contract.write.contribute([fourthContribution], { account: secondWallet.account });
    console.log(`\nSecond wallet contributed: ${formatUnits(fourthContribution, 18)} UPD in third cycle`);

    totalTokens = await contract.read.tokens();
    contributorFees = await contract.read.contributorFees();
    console.log(`After third cycle contribution:`);
    console.log(`  Total tokens: ${formatUnits(totalTokens, 18)} UPD`);
    console.log(`  Contributor fees: ${formatUnits(contributorFees, 18)} UPD`);

    // Now perform a large airdrop
    await time.increase(Number(cycleLength) + 1);
    const airdropAmount = parseUnits('100000', 18);
    await contract.write.airdrop([airdropAmount], { account: airdropperWallet.account });
    console.log(`\nAirdropped: ${formatUnits(airdropAmount, 18)} UPD`);

    totalTokens = await contract.read.tokens();
    contributorFees = await contract.read.contributorFees();
    console.log(`After airdrop:`);
    console.log(`  Total tokens: ${formatUnits(totalTokens, 18)} UPD`);
    console.log(`  Contributor fees: ${formatUnits(contributorFees, 18)} UPD`);

    // The airdrop should add the entire amount (minus anti-spam fee) to contributorFees
    const expectedAirdropFee = airdropAmount - antiSpamFee; // Entire amount minus anti-spam fee
    console.log(`  Expected contributor fee from airdrop: ${formatUnits(expectedAirdropFee, 18)} UPD`);

    // Advance time to distribute fees
    await time.increase(Number(cycleLength) + 1);
    await contract.write.contribute([antiSpamFee * 2n]);

    // Check positions before withdrawals
    console.log('\n=== Positions Before Withdrawals ===');
    const firstPosition = await contract.read.checkPosition([firstWallet.account.address, 0]);
    const firstPosition2 = await contract.read.checkPosition([firstWallet.account.address, 1]);
    const secondPosition = await contract.read.checkPosition([secondWallet.account.address, 0]);
    const secondPosition2 = await contract.read.checkPosition([secondWallet.account.address, 1]);
    
    console.log(`First wallet position 1: ${formatUnits(firstPosition[0], 18)} UPD, shares: ${formatUnits(firstPosition[1], 18)}`);
    console.log(`First wallet position 2: ${formatUnits(firstPosition2[0], 18)} UPD, shares: ${formatUnits(firstPosition2[1], 18)}`);
    console.log(`Second wallet position 1: ${formatUnits(secondPosition[0], 18)} UPD, shares: ${formatUnits(secondPosition[1], 18)}`);
    console.log(`Second wallet position 2: ${formatUnits(secondPosition2[0], 18)} UPD, shares: ${formatUnits(secondPosition2[1], 18)}`);

    // Calculate total expected withdrawals
    const totalExpectedWithdrawals = firstPosition[0] + firstPosition2[0] + secondPosition[0] + secondPosition2[0];
    console.log(`\nTotal expected withdrawals: ${formatUnits(totalExpectedWithdrawals, 18)} UPD`);

    totalTokens = await contract.read.tokens();
    contributorFees = await contract.read.contributorFees();
    console.log(`Current total tokens: ${formatUnits(totalTokens, 18)} UPD`);
    console.log(`Current contributor fees: ${formatUnits(contributorFees, 18)} UPD`);

    // Check if contributorFees is sufficient for all expected withdrawals
    const totalFeesNeeded = totalExpectedWithdrawals - (initialContribution + secondContribution + thirdContribution + fourthContribution);
    console.log(`Total fees needed for withdrawals: ${formatUnits(totalFeesNeeded, 18)} UPD`);
    
    if (totalFeesNeeded > contributorFees) {
      console.log(`🐛 POTENTIAL ISSUE: Need ${formatUnits(totalFeesNeeded, 18)} UPD in fees but only have ${formatUnits(contributorFees, 18)} UPD tracked!`);
      console.log(`This will cause fee capping and tokens to be stuck in the contract.`);
    }

    // Now perform withdrawals and see what happens
    console.log('\n=== Performing Withdrawals ===');
    
    // First wallet withdraws first position
    const firstBalanceBefore = await upd.read.balanceOf([firstWallet.account.address]);
    await contract.write.withdraw([0]);
    const firstBalanceAfter = await upd.read.balanceOf([firstWallet.account.address]);
    const firstWithdrawn = firstBalanceAfter - firstBalanceBefore;
    
    console.log(`First wallet withdrew position 1: ${formatUnits(firstWithdrawn, 18)} UPD (expected: ${formatUnits(firstPosition[0], 18)})`);
    
    // Check contributorFees after first withdrawal
    contributorFees = await contract.read.contributorFees();
    console.log(`Contributor fees after first withdrawal: ${formatUnits(contributorFees, 18)} UPD`);

    // Second wallet withdraws first position
    const secondBalanceBefore = await upd.read.balanceOf([secondWallet.account.address]);
    await contract.write.withdraw([0], { account: secondWallet.account });
    const secondBalanceAfter = await upd.read.balanceOf([secondWallet.account.address]);
    const secondWithdrawn = secondBalanceAfter - secondBalanceBefore;
    
    console.log(`Second wallet withdrew position 1: ${formatUnits(secondWithdrawn, 18)} UPD (expected: ${formatUnits(secondPosition[0], 18)})`);
    
    // Check contributorFees after second withdrawal
    contributorFees = await contract.read.contributorFees();
    console.log(`Contributor fees after second withdrawal: ${formatUnits(contributorFees, 18)} UPD`);

    // Continue with remaining withdrawals...
    await contract.write.withdraw([1]);
    await contract.write.withdraw([1], { account: secondWallet.account });

    // Check final state
    const finalTokens = await contract.read.tokens();
    const finalContributorFees = await contract.read.contributorFees();
    console.log(`\n=== Final State ===`);
    console.log(`Final tokens remaining: ${formatUnits(finalTokens, 18)} UPD`);
    console.log(`Final contributor fees: ${formatUnits(finalContributorFees, 18)} UPD`);

    if (finalTokens > parseUnits('100', 18)) {
      console.log(`🐛 CONFIRMED: ${formatUnits(finalTokens, 18)} UPD stuck due to contributor fees tracking issue!`);
    }
  });
});
