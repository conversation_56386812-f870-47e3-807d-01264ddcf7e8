import hre from 'hardhat';
import { parseUnits, toHex, formatUnits } from 'viem';
import { expect } from 'chai';
import { loadFixture } from '@nomicfoundation/hardhat-toolbox-viem/network-helpers';
import { getEventsFromTx, walletAddress } from './utilities/helpers.ts';
import { time } from "@nomicfoundation/hardhat-network-helpers";

// Constants for testing
const antiSpamFee = parseUnits('1', 18);

const deployUpdraft = async () => {
  const upd = await hre.viem.deployContract('UPDToken');
  const feeToken = upd.address;
  const percentFee = 10000; // 1%
  const accrualRate = 100000; // 10% - higher for easier testing
  const cycleLength = 3600; // 1 hour in seconds
  const humanity = '******************************************';
  const args = [feeToken, antiSpamFee, percentFee, cycleLength, accrualRate, humanity];
  const updraft = await hre.viem.deployContract('Updraft', args);
  return { updraft, upd, humanity };
};

const deployUpdraftAndApproveToSpendUPD = async () => {
  const { updraft, upd, humanity } = await loadFixture(deployUpdraft);
  // Approve a very large amount to avoid allowance issues
  await upd.write.approve([updraft.address, parseUnits('10000000', 18)]);
  return { updraft, upd, humanity };
};

describe('Shares Calculation Bug', () => {
  it('should demonstrate the shares calculation bug in positionToLastStoredCycle', async () => {
    const { updraft, upd, humanity } = await loadFixture(deployUpdraftAndApproveToSpendUPD);
    const publicClient = await hre.viem.getPublicClient();

    // Create a new idea with 10% contributor fee
    const contributorFee = 100000; // 10%
    const initialContribution = parseUnits('1000', 18);
    const hash = await updraft.write.createIdea([contributorFee, initialContribution, toHex({})]);
    const transaction = await publicClient.getTransactionReceipt({hash});
    const events = await getEventsFromTx('Updraft', transaction);
    const { idea } = events.find(event => event.eventName === 'IdeaCreated').args;
    const contract = await hre.viem.getContractAt('Idea', idea);

    // Get wallet clients
    const [firstWallet, secondWallet] = await hre.viem.getWalletClients();
    
    // Transfer tokens to test wallets
    const transferAmount = parseUnits('100000', 18);
    await upd.write.transfer([secondWallet.account.address, transferAmount]);
    await upd.write.approve([contract.address, transferAmount], { account: secondWallet.account });
    await upd.write.approve([contract.address, parseUnits('100000', 18)]);

    console.log('\n=== Setting up the bug scenario ===');
    
    // Second wallet contributes in first cycle
    const secondContribution = parseUnits('2000', 18);
    await contract.write.contribute([secondContribution], { account: secondWallet.account });
    console.log(`Second wallet contributed: ${formatUnits(secondContribution, 18)} UPD in first cycle`);

    // Get cycle length
    const cycleLength = await contract.read.cycleLength();
    const accrualRate = await contract.read.accrualRate();
    const percentScale = await contract.read.percentScale();
    
    console.log(`Accrual rate: ${Number(accrualRate) / 10000}%`);
    console.log(`Cycle length: ${cycleLength} seconds`);

    // Advance to second cycle and make contributions to create multiple cycles with fees
    console.log('\n=== Creating multiple cycles with fees ===');
    
    for (let cycle = 2; cycle <= 5; cycle++) {
      await time.increase(Number(cycleLength) + 1);
      
      // Make a contribution to create fees in this cycle
      const contribution = parseUnits('500', 18);
      await contract.write.contribute([contribution]);
      console.log(`Cycle ${cycle}: Made contribution of ${formatUnits(contribution, 18)} UPD`);
      
      // Check the position after each cycle
      const position = await contract.read.checkPosition([secondWallet.account.address, 0]);
      console.log(`  Second wallet position: ${formatUnits(position[0], 18)} UPD, shares: ${formatUnits(position[1], 18)}`);
    }

    // Now let's manually calculate what the shares should be vs what they actually are
    console.log('\n=== Analyzing the shares calculation bug ===');
    
    // Get the position details
    const positionData = await contract.read.positionsByAddress([secondWallet.account.address, 0n]);
    const startCycleIndex = positionData[0];
    const originalTokens = positionData[1];
    
    console.log(`Position start cycle index: ${startCycleIndex}`);
    console.log(`Position original tokens: ${formatUnits(originalTokens, 18)} UPD`);
    
    // Get all cycles
    const cyclesLength = await contract.read.cycles([0]); // This will fail, let me get the length differently
    
    // Let's check a few cycles manually
    for (let i = 0; i < 6; i++) {
      try {
        const cycle = await contract.read.cycles([BigInt(i)]);
        console.log(`Cycle ${i}: number=${cycle[0]}, shares=${formatUnits(cycle[1], 18)}, fees=${formatUnits(cycle[2], 18)}, hasContributions=${cycle[3]}`);
      } catch (e) {
        console.log(`Cycle ${i}: does not exist`);
        break;
      }
    }

    // The bug is that in positionToLastStoredCycle, the shares calculation:
    // shares = accrualRate * (cycle.number - firstCycleNumber) * originalTokens / percentScale;
    // overwrites the shares variable instead of accumulating it.
    
    // This means only the shares from the LAST cycle are used for fee distribution,
    // causing massive underdistribution of fees.
    
    console.log('\n=== Demonstrating the impact ===');
    
    // Get total tokens and contributor fees before withdrawal
    const totalTokensBefore = await contract.read.tokens();
    const contributorFeesBefore = await contract.read.contributorFees();
    console.log(`Total tokens before withdrawal: ${formatUnits(totalTokensBefore, 18)} UPD`);
    console.log(`Contributor fees before withdrawal: ${formatUnits(contributorFeesBefore, 18)} UPD`);
    
    // Second wallet withdraws
    const balanceBefore = await upd.read.balanceOf([secondWallet.account.address]);
    await contract.write.withdraw([0], { account: secondWallet.account });
    const balanceAfter = await upd.read.balanceOf([secondWallet.account.address]);
    const withdrawn = balanceAfter - balanceBefore;
    
    console.log(`Second wallet withdrew: ${formatUnits(withdrawn, 18)} UPD`);
    console.log(`Second wallet deposited: ${formatUnits(secondContribution, 18)} UPD`);
    console.log(`Second wallet gain: ${formatUnits(withdrawn - secondContribution, 18)} UPD`);
    
    // Check remaining tokens - this should be much higher than expected due to the bug
    const totalTokensAfter = await contract.read.tokens();
    const contributorFeesAfter = await contract.read.contributorFees();
    console.log(`\nTotal tokens after withdrawal: ${formatUnits(totalTokensAfter, 18)} UPD`);
    console.log(`Contributor fees after withdrawal: ${formatUnits(contributorFeesAfter, 18)} UPD`);
    
    // The bug causes tokens to be stuck in the contract
    if (totalTokensAfter > parseUnits('100', 18)) {
      console.log(`🐛 BUG CONFIRMED: ${formatUnits(totalTokensAfter, 18)} UPD stuck in contract due to shares calculation bug!`);
      console.log(`This happens because positionToLastStoredCycle overwrites shares instead of accumulating them.`);
    }
    
    // Let's also withdraw the first wallet's position to see the total impact
    const firstBalanceBefore = await upd.read.balanceOf([firstWallet.account.address]);
    await contract.write.withdraw([0]);
    const firstBalanceAfter = await upd.read.balanceOf([firstWallet.account.address]);
    const firstWithdrawn = firstBalanceAfter - firstBalanceBefore;
    
    console.log(`\nFirst wallet withdrew: ${formatUnits(firstWithdrawn, 18)} UPD`);
    console.log(`First wallet deposited: ${formatUnits(initialContribution, 18)} UPD`);
    
    const finalTokens = await contract.read.tokens();
    console.log(`\nFinal tokens remaining: ${formatUnits(finalTokens, 18)} UPD`);
    
    if (finalTokens > parseUnits('100', 18)) {
      console.log(`🐛 MASSIVE BUG: ${formatUnits(finalTokens, 18)} UPD permanently stuck due to shares calculation error!`);
    }
  });
});
